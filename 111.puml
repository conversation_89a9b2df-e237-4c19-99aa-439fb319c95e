@startuml Club入会操作时序图
!theme plain
skinparam backgroundColor #FFFFFF
skinparam sequenceArrowThickness 2
skinparam roundcorner 20
skinparam maxmessagesize 60
skinparam sequenceParticipant underline

actor Client as "客户端"
participant MemberBiz as "MemberBiz"
participant Validator as "入会验证器"
participant OrderProxy as "订单服务代理"
participant MemberInfoService as "MemberInfoService"
participant ClubMemberInfoEngine as "会员信息引擎"
participant AdvisorPoolService as "顾问池服务"
participant ClubMemberRecordEngine as "会员记录引擎"
participant ClubMemberBenefitEngine as "会员权益引擎"
participant EventMessageService as "事件消息服务"
participant EventPublisher as "事件发布器"
database Database as "数据库"

title Club入会操作完整流程

== 1. 入会请求阶段 ==
Client -> MemberBiz ++: join(midStr, orderId)
note right of MemberBiz: 开始入会流程
MemberBiz -> MemberBiz: 解析用户ID: Long.parseLong(midStr)
MemberBiz -> MemberBiz: 记录日志: "Initiating member join"

== 2. 入会验证阶段 ==
MemberBiz -> Validator ++: validateJoinRequest(midStr, orderId)
Validator -> ClubMemberInfoEngine ++: findActiveMemberByMidFromMaster(midStr)
ClubMemberInfoEngine -> Database ++: 查询活跃会员信息
Database --> ClubMemberInfoEngine --: 返回会员信息
ClubMemberInfoEngine --> Validator --: 返回活跃会员

alt 如果用户已是会员
    Validator -> ClubMemberRecordEngine ++: queryActiveByMid(midStr)
    ClubMemberRecordEngine -> Database ++: 查询活跃会员记录
    Database --> ClubMemberRecordEngine --: 返回会员记录列表
    ClubMemberRecordEngine --> Validator --: 返回活跃记录

    alt 如果是重复请求
        Validator -> Validator: 检查订单ID是否相同
        note right of Validator: 幂等性检查，直接返回
        Validator --> MemberBiz: 返回成功（幂等）
    else 如果是不同订单
        Validator -> Validator: 抛出异常: JOIN_FAIL
        Validator --> MemberBiz --: 验证失败
    end
else 如果用户不是会员
    Validator --> MemberBiz --: 验证通过
end

== 3. 获取订单详情阶段 ==
MemberBiz -> OrderProxy ++: fetchOrderDetailsWithRetry(mid, orderId)
note right of OrderProxy: 最多重试3次
loop 重试机制
    OrderProxy -> OrderProxy: commonOrderDetail(mid, orderId)
    OrderProxy -> OrderProxy: extractMemberInfo(orderDetailResp)
    OrderProxy -> OrderProxy: 设置订单创建时间和支付时间
end
OrderProxy --> MemberBiz --: 返回MemberOrderInfo

== 4. 构建入会事件阶段 ==
MemberBiz -> MemberBiz: 创建MemberJoinedEvent
MemberBiz -> MemberBiz: 设置事件ID、时间戳、用户ID等
MemberBiz -> MemberBiz: 构建MemberTxData事务数据

== 5. 执行入会核心逻辑阶段 ==
MemberBiz -> MemberInfoService ++: doJoin(txData, benefitConfig)
note over MemberInfoService: 开始事务

=== 5.1 构建会员基本信息 ===
MemberInfoService -> MemberInfoService: buildClubMemberInfo(txData)
note right of MemberInfoService: 设置mid、vid=null、clubNo=null\nlevel=SVIP、status=ACTIVE

=== 5.2 查询已存在会员信息 ===
MemberInfoService -> ClubMemberInfoEngine ++: findMemberByMid(midStr)
ClubMemberInfoEngine -> Database ++: 查询会员信息
Database --> ClubMemberInfoEngine --: 返回已存在会员信息
ClubMemberInfoEngine --> MemberInfoService --: 返回existedMemberInfo

=== 5.3 获取顾问 ===
MemberInfoService -> AdvisorPoolService ++: getNextAdvisor()
AdvisorPoolService -> Database ++: 查询可用顾问
Database --> AdvisorPoolService --: 返回顾问信息
AdvisorPoolService --> MemberInfoService --: 返回advisor

MemberInfoService -> MemberInfoService: 设置顾问ID到会员信息

alt 如果存在已有会员信息
    MemberInfoService -> MemberInfoService: 设置已有会员ID（幂等处理）
end

=== 5.4 保存会员基本信息 ===
MemberInfoService -> ClubMemberInfoEngine ++: saveOrUpdate(clubMemberInfo)
ClubMemberInfoEngine -> Database ++: 保存或更新会员基本信息
Database --> ClubMemberInfoEngine --: 返回操作结果
ClubMemberInfoEngine --> MemberInfoService --: 返回保存结果

alt 保存失败
    MemberInfoService -> MemberInfoService: 抛出异常: "saveOrUpdate clubMemberInfo status failed"
end

=== 5.5 构建并保存会员记录 ===
MemberInfoService -> MemberInfoService: buildClubMemberRecord(txData)
note right of MemberInfoService: 设置订单信息、状态、有效期等

MemberInfoService -> ClubMemberRecordEngine ++: save(clubMemberRecord)
ClubMemberRecordEngine -> Database ++: 保存会员记录
Database --> ClubMemberRecordEngine --: 返回操作结果
ClubMemberRecordEngine --> MemberInfoService --: 返回保存结果

alt 保存失败
    MemberInfoService -> MemberInfoService: 抛出异常: "save clubMemberRecord status failed"
end

=== 5.6 初始化权益信息 ===
MemberInfoService -> MemberInfoService: buildInitBenefitEntity(txData, startTime, endTime, benefitConfig)
note right of MemberInfoService: 根据配置初始化所有权益

loop 遍历权益配置
    MemberInfoService -> MemberInfoService: 创建ClubMemberBenefitEntity
    MemberInfoService -> MemberInfoService: 设置权益基本信息（mid、benefitId、benefitKey等）
    MemberInfoService -> MemberInfoService: 设置权益归属（人员/车辆）
    MemberInfoService -> MemberInfoService: 设置权益状态为INIT
    MemberInfoService -> MemberInfoService: 设置权益数据和有效期
end

MemberInfoService -> ClubMemberBenefitEngine ++: saveBatch(benefitEntities)
ClubMemberBenefitEngine -> Database ++: 批量保存权益信息
Database --> ClubMemberBenefitEngine --: 返回操作结果
ClubMemberBenefitEngine --> MemberInfoService --: 返回保存结果

alt 保存失败
    MemberInfoService -> MemberInfoService: 抛出异常: "saveBatch benefitEntities status failed"
end

=== 5.7 更新顾问分配数量 ===
MemberInfoService -> AdvisorPoolService ++: increaseAssignedCount(advisorId)
AdvisorPoolService -> Database ++: 更新顾问分配计数
Database --> AdvisorPoolService --: 返回操作结果
AdvisorPoolService --> MemberInfoService --: 返回更新结果

alt 更新失败
    MemberInfoService -> MemberInfoService: 抛出异常: "increaseAssignedCount status failed"
end

=== 5.8 保存事件消息 ===
MemberInfoService -> EventMessageService ++: saveEventMessage(clubEvent)
EventMessageService -> Database ++: 保存事件消息
Database --> EventMessageService --: 返回操作结果
EventMessageService --> MemberInfoService --: 返回保存结果

note over MemberInfoService: 提交事务
MemberInfoService --> MemberBiz --: 返回成功

== 6. 发布事件阶段 ==
MemberBiz -> EventPublisher ++: publishEvent(memberJoinedEvent)
EventPublisher -> EventPublisher: 发布入会成功事件
EventPublisher --> MemberBiz --: 事件发布完成

== 7. 完成阶段 ==
MemberBiz -> MemberBiz: 记录日志: "Member join successful"
MemberBiz --> Client --: 返回入会成功

note over Client, Database: 入会流程完成

== 异常处理流程 ==
note over MemberBiz, Database: 异常处理机制
alt 任何步骤失败
    Database -> Database: 事务回滚
    MemberInfoService -> MemberBiz: 抛出具体异常
    MemberBiz -> Client: 返回错误信息
end

@enduml