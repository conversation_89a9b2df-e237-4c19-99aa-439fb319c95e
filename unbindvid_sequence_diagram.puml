@startuml
actor User
participant "MemberBiz" as MemberBiz
participant "clubMemberInfoEngine" as clubMemberInfoEngine
participant "log" as log
participant "memberRecordEngine" as memberRecordEngine
participant "VehicleUnBindEvent" as VehicleUnBindEvent
participant "Payload" as Payload
participant "memberBenefitService" as memberBenefitService
participant "benefitBiz" as benefitBiz
participant "MemberTxData" as MemberTxData
participant "memberInfoService" as memberInfoService

User -> MemberBiz: unBindVid(unbindReq)
activate MemberBiz

MemberBiz -> clubMemberInfoEngine: findActiveMemberByMid(unbindReq.getMid())
activate clubMemberInfoEngine
clubMemberInfoEngine --> MemberBiz: clubMemberInfo

MemberBiz -> MemberBiz: check clubMemberInfo == null
activate log
MemberBiz -> log: warn Not club member
log --> MemberBiz

MemberBiz -> MemberBiz: check vid is empty
activate log
MemberBiz -> log: warn member no bind vid
log --> MemberBiz

MemberBiz -> MemberBiz: check vid equals
activate log
MemberBiz -> log: warn vid is not same
log --> MemberBiz

MemberBiz -> memberRecordEngine: queryActiveByMid(unbindReq.getMid())
activate memberRecordEngine
memberRecordEngine --> MemberBiz: clubMemberRecordEntity

MemberBiz -> VehicleUnBindEvent: new()
activate VehicleUnBindEvent
VehicleUnBindEvent --> MemberBiz: vehicleUnBindEvent

MemberBiz -> Payload: new()
activate Payload
Payload --> MemberBiz: payload

MemberBiz -> VehicleUnBindEvent: setPayload(payload)

MemberBiz -> memberBenefitService: getVehicleBenefitList(clubMemberInfo.getMid(), clubMemberRecordEntity.getOrderId(), unbindReq.getVid())
activate memberBenefitService
memberBenefitService --> MemberBiz: vehicleBenefitList

MemberBiz -> MemberBiz: check vehicleBenefitList is not empty
activate benefitBiz
MemberBiz -> benefitBiz: revokeBenefit(vehicleBenefitList, RevokeScene.VEHICLE_UNBIND)
benefitBiz --> MemberBiz

MemberBiz -> MemberTxData: new()
activate MemberTxData
MemberTxData --> MemberBiz: memberTxData

MemberBiz -> memberInfoService: unBindVid(memberTxData, vehicleUnBindEvent)
activate memberInfoService
memberInfoService --> MemberBiz

MemberBiz -> log: info Successfully unbound vid
@enduml